{% extends 'main/base.html' %}
{% block title %}
    User Profile
{% endblock %}

{% block content %}
    {% if user.is_customer %}
        <div style="display: ruby;">
            <h1>{{ user.username }} ({{user_age}} y/o)</h1>
            <p>{{ user.email }}</p>
            <p style="float: right;">Customer</p>
        </div>
    {% else %}
        <div style="display: ruby;">
            <h1>{{ user.username }}</h1>
            <p> {{ user.email }}</p>
            <p style="float: right;">{{user.company.field}} Company</p>
        </div>
       {% endif %}
    {% if 'customer' in request.path %}
        <p class="title">Previous Requested Services</p>
    {% else %}
        <p class="title">Available Services</p>
    {% endif %}
    {% if user.is_customer %}
        {% for service in sh %}
            <div>
                <a href="/services/{{service.service.id}}">{{service.service.name}}</a> ({{service.service.field}})
                <p style="margin: 0; display: inline-block;"> ❱❱ {{service.price}}</p>
                <p style="margin: 0; display: inline-block;"> ❱❱ {{ service.request_date }}</p>
                <p style="float: right; margin: 0;">
                    by <a href="/company/{{service.service.company.user}}">{{service.service.company.user}}</a>
                </p> 
            </div>
            <div class="line"></div>
        {% endfor %}
    {% else %}
        {% for service in services %}
        <div class="list_services_profile">
            <a href="/services/{{service.id}}">{{service.name}}</a>-- {{service.price_hour}}€/hour
            <div class="line"></div>
        </div>
        {% endfor %}
    {% endif %}
{% endblock %}