Functional
Register a new Customer.
When registering a Customer, does the website request a username, an email, a password, a password confirmation and a date of birth?
Register a new electricity Company.
When registering a Company, does the website request a username, an email, a password, a password confirmation and a field of work?
Is the field of work restricted in a way that it only accepts one of the following values: Air Conditioner, All in One, Carpentry, Electricity, Gardening, Home Machines, Housekeeping, Interior Design, Locks, Painting, Plumbing, Water Heaters?
Are you able to register two different types of users (Customers and Companies)?
Try to register a new user (Customer or Company) and use a username that already exists.
Were you warned that a user with that username already exists?
Try to register a new user (Customer or Company) and use an email that already exists.
Were you warned that a user with that email already exists?
After registering and logging in with a Company profile, navigate to the profile page.
Is all its information available (apart from the password)?
While logged in with the electricity Company, create a new service with a price per hour of 10.50.
Were you asked for a name, a description, a price and a field?
Naviagate to the user (Company) profile.
Did the service created before, appears in the company page as an available service?
Is there a page showing every service created by every company?
Is there a page for every type of service which displays every service of that type?
Does a service have its own page, where its information gets displayed (name, description, field, price per hour and date it was created) along with the name of the company which created it?
Logout from the Company profile and after registering and being logged in with a Customer account, navigate to the profile page.
Is all his or her information available (apart from the password)?
Go to the previous created service and request it with a 2 hours interval.
Were you asked for an address and a service time (in hours)?
Go to the user (Customer) profile.
Does the service requested before, appears in the customer page as a previously requested service?
Does the service requested appear with the price of 21.00 (2 hours * 10.50)?
Is there a page showing the most requested services from the whole website?
Logout and register a new All in One Company and navigate to the service creation page.
Can you choose between all of the types of service for this new service?
Create a new Painting service. Logout from the Company and login with a Customer. Request two times that same service and go to the most requested services page.
Is the list of most requested services updated with this new service?
Basic
+Does the code obey the good practices?
Bonus
+Is a system implemented where Customers can rate the services they have requested?
+Is there a page system in the service list page?
+Are there any other bonuses implemented?
+Did the student implement its own display and design?
Social
+Did you learn anything from this project?
+Would you recommend/nominate this program as an example for the rest of the school?