{% extends 'main/base.html' %} {% block title %} {{ user.username }}'s Profile
{% endblock %} {% block content %}
<div class="content">
  <div class="profile-container">
    <div class="profile-header">
      <h1 class="profile-title">{{ user.username }}'s Profile</h1>
      <span class="profile-type">Customer</span>
    </div>

    <div class="profile-info">
      <h3>Account Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Email</label>
          <span>{{ user.email }}</span>
        </div>
        <div class="info-item">
          <label>Date of Birth</label>
          <span>{{ user.customer.birth }}</span>
        </div>
      </div>
    </div>

    <div class="profile-section">
      <h3>Requested Services</h3>
      {% if requested_services %}
      <div class="services_list">
        {% for requested in requested_services %}
        <div class="service-item">
          <div class="service_list_info">
            <h4>
              <a href="/services/{{ requested.service.id }}"
                >{{ requested.service.name }}</a
              >
            </h4>
            <p class="service-field">{{ requested.service.field }}</p>
            <p class="service-company">
              by
              <a href="/company/{{ requested.service.company.user.username }}"
                >{{ requested.service.company.user.username }}</a
              >
            </p>
            <div class="request-details">
              <div class="detail-item">
                <label>Address:</label>
                <span>{{ requested.address }}</span>
              </div>
              <div class="detail-item">
                <label>Hours:</label>
                <span>{{ requested.hours }}</span>
              </div>
              <div class="detail-item">
                <label>Total Cost:</label>
                <span>${{ requested.total_cost }}</span>
              </div>
              <div class="detail-item">
                <label>Requested:</label>
                <span>{{ requested.date_requested|date:"F j, Y" }}</span>
              </div>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
      {% else %}
      <div class="no-services-message">
        <p>No services have been requested yet.</p>
        <a href="/services/" class="btn btn-primary">Browse Services</a>
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
