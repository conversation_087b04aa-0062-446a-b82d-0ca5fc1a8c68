{% extends 'main/base.html' %}
{% block title %}
    Services List
{% endblock %}
{% block content %}
    <p class="title">Services</p>
    {% if user.is_company %}
        <a class="create_service" href="/services/create"> Create Service</a>
    {% endif %}
    <div class='services_list'>
        {% if services %}
            {% for service in services %}
                <div class="service-item">
                    <div class='service_list_info'>
                        <h3><a href="/services/{{service.id}}">{{ service.name }}</a></h3>
                        <p class="service-price">{{ service.price_hour }}€/hour</p>
                        <p class="service-field">{{ service.field }}</p>
                        <p class="service-description">{{ service.description }}</p>
                        <p class="service-company">by <a href="/company/{{service.company.user.username}}">{{service.company.user.username}}</a></p>
                    </div>
                </div>
                {% if not forloop.last %}
                    <div class="line"></div>
                {% endif %}
            {% endfor %}
        {% else %}
            <h2>Sorry No services available yet</h2>
        {% endif %}
     </div>
{% endblock %}
