from django.contrib import admin

from .models import Service, RequestedService


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "price_hour", "field", "date", "company")
    search_fields = ('name', 'field', 'company__user__username')
    list_filter = ('field', 'date')


@admin.register(RequestedService)
class RequestedServiceAdmin(admin.ModelAdmin):
    list_display = ('service', 'customer', 'address', 'hours', 'total_cost', 'date_requested')
    search_fields = ('service__name', 'customer__user__username', 'address')
    list_filter = ('date_requested', 'service__field')
