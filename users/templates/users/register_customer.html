{% extends 'main/base.html' %}

{% block title %}
    Customer Registration
{% endblock %}

{% block content %}
<div class="content">
    <h1 class="title">Customer Registration</h1>
    
    <div class="registration-container">
        <form method="POST" class="modern-form">
            {% csrf_token %}
            
            <div class="form-section">
                <h3 class="section-title">Account Information</h3>
                <div class="form-group">
                    <label class="form-label">Username</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="error-message">{{ form.username.errors }}</div>
                    {% endif %}
                </div>
            </div>

            <div class="form-section">
                <h3 class="section-title">Contact Information</h3>
                <div class="form-group">
                    <label class="form-label">Email Address</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="error-message">{{ form.email.errors }}</div>
                    {% endif %}
                </div>
            </div>

            <div class="form-section">
                <h3 class="section-title">Security</h3>
                <div class="form-group">
                    <label class="form-label">Password</label>
                    {{ form.password1 }}
                    {% if form.password1.errors %}
                        <div class="error-message">{{ form.password1.errors }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    <label class="form-label">Confirm Password</label>
                    {{ form.password2 }}
                    {% if form.password2.errors %}
                        <div class="error-message">{{ form.password2.errors }}</div>
                    {% endif %}
                </div>
            </div>

            <div class="form-section">
                <h3 class="section-title">Personal Information</h3>
                <div class="form-group">
                    <label class="form-label">Date of Birth</label>
                    {{ form.birth }}
                    {% if form.birth.errors %}
                        <div class="error-message">{{ form.birth.errors }}</div>
                    {% endif %}
                    <small class="help-text">{{ form.birth.help_text }}</small>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Register as Customer</button>
                <a href="/register/" class="btn btn-secondary">Back to Registration Options</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
