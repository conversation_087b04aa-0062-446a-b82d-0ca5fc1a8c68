{% extends 'main/base.html' %} {% block title %} {{ field }} Services {%
endblock %} {% block content %}
<div class="content">
  <h1 class="title">{{ field }} Services</h1>

  {% if services %}
  <div class="services_list">
    {% for service in services %}
    <div class="service-item">
      <div class="service_list_info">
        <h3><a href="/services/{{ service.id }}">{{ service.name }}</a></h3>
        <p class="service-price">{{ service.price_hour }}€/hour</p>
        <p class="service-field">{{ service.field }}</p>
        <p class="service-description">{{ service.description }}</p>
        <p class="service-company">
          by
          <a href="/company/{{ service.company.user.username }}"
            >{{ service.company.user.username }}</a
          >
        </p>
      </div>
    </div>
    {% endfor %}
  </div>
  {% else %}
  <div class="no-services-message">
    <h2>No {{ field }} services available</h2>
    <p>Check back later or browse other service categories.</p>
    <a href="/services/" class="btn btn-primary">View All Services</a>
  </div>
  {% endif %}
</div>
{% endblock %}
