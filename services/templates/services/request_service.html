{% extends 'main/base.html' %} {% block title %} Request Service {% endblock %}
{% block content %}
<div class="content">
  <h1 class="title">Request Service</h1>

  <div class="service-request-container">
    <div class="service-request-info">
      <h2>{{ service.name }}</h2>
      <p class="service-request-price">{{ service.price_hour }}€/hour</p>
      <p class="service-request-description">{{ service.description }}</p>
    </div>

    <form method="POST" class="modern-form">
      {% csrf_token %}

      <div class="form-section">
        <h3 class="section-title">Request Details</h3>
        <div class="form-group">
          <label class="form-label">Service Address</label>
          {{ form.address }} {% if form.address.errors %}
          <div class="error-message">{{ form.address.errors }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label class="form-label">Estimated Hours</label>
          {{ form.hours }} {% if form.hours.errors %}
          <div class="error-message">{{ form.hours.errors }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-actions">
        <button type="submit" class="btn btn-primary">Submit Request</button>
        <a href="/services/{{ service.id }}/" class="btn btn-secondary"
          >Cancel</a
        >
      </div>
    </form>
  </div>
</div>
{% endblock %}
